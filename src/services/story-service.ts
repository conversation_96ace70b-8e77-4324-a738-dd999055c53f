import { prisma } from '@/lib/prisma';
import { generateStoryOutline } from '@/lib/ai';
import { StoryFormData } from '@/store/story-creation';

export interface CreateStoryInput extends StoryFormData {
  userId: string;
}

export interface StoryResponse {
  id: string;
  title: string;
  protagonistName: string;
  theme: string;
  status: 'draft' | 'generating' | 'completed';
  outline?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function createStory(input: CreateStoryInput): Promise<StoryResponse> {
  const {
    userId,
    protagonistName,
    protagonistPersonality,
    theme,
    keyElements,
    style,
    ageGroup,
    moral,
  } = input;

  // 生成故事标题
  const title = `${protagonistName}的${theme}故事`;

  // 生成故事大纲
  const outline = await generateStoryOutline({
    protagonistName,
    protagonistPersonality,
    theme,
    keyElements,
    style,
    ageGroup,
    moral,
  });

  // 创建故事记录
  const story = await prisma.story.create({
    data: {
      title,
      protagonist<PERSON><PERSON>,
      protagonistPersonality,
      theme,
      keyElements,
      style,
      ageGroup,
      moral,
      outline,
      status: 'draft',
      userId,
    },
  });

  return {
    id: story.id,
    title: story.title,
    protagonistName: story.protagonistName,
    theme: story.theme,
    status: story.status,
    outline: story.outline || undefined,
    userId: story.userId,
    createdAt: story.createdAt,
    updatedAt: story.updatedAt,
  };
}

export async function getStoryById(id: string, userId: string) {
  const story = await prisma.story.findFirst({
    where: {
      id,
      userId,
    },
    include: {
      chapters: {
        orderBy: {
          order: 'asc',
        },
      },
    },
  });

  if (!story) {
    throw new Error('Story not found');
  }

  return story;
}

export async function updateStoryStatus(
  id: string,
  userId: string,
  status: 'draft' | 'generating' | 'completed'
) {
  const story = await prisma.story.update({
    where: {
      id,
      userId,
    },
    data: {
      status,
    },
  });

  return story;
}

export async function addStoryChapter(
  storyId: string,
  userId: string,
  chapter: {
    title: string;
    content: string;
    imageUrl?: string;
    order: number;
  }
) {
  const story = await prisma.story.findFirst({
    where: {
      id: storyId,
      userId,
    },
  });

  if (!story) {
    throw new Error('Story not found');
  }

  const newChapter = await prisma.chapter.create({
    data: {
      storyId,
      title: chapter.title,
      content: chapter.content,
      imageUrl: chapter.imageUrl,
      order: chapter.order,
    },
  });

  return newChapter;
}