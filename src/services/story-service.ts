import { prisma } from '@/lib/prisma';
import { generateStoryOutline } from '@/lib/ai';
import { StoryFormData } from '@/store/story-creation';

export interface CreateStoryInput extends StoryFormData {
  userId: string;
}

export interface StoryResponse {
  id: string;
  title: string;
  protagonistName: string;
  theme: string;
  status: 'draft' | 'generating' | 'completed';
  outline?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function createStory(input: CreateStoryInput): Promise<StoryResponse> {
  const {
    userId,
    protagonistName,
    protagonistPersonality,
    theme,
    keyElements,
    style,
    ageGroup,
    moral,
  } = input;

  // 生成故事标题
  const title = `${protagonistName}的${theme}故事`;

  // 生成故事大纲
  const outline = await generateStoryOutline({
    protagonistName,
    protagonistPersonality,
    theme,
    keyElements,
    style,
    ageGroup,
    moral,
  });

  // 准备故事数据
  const storyData = {
    protagonistName,
    protagonistPersonality,
    theme,
    keyElements,
    style,
    ageGroup,
    moral,
    outline,
  };

  // 创建故事记录
  const story = await prisma.story.create({
    data: {
      title,
      description: `一个关于${protagonistName}的${theme}故事`,
      story_data: storyData,
      status: 'draft',
      user_id: BigInt(userId),
    },
  });

  return {
    id: story.id.toString(),
    title: story.title,
    protagonistName,
    theme,
    status: story.status as 'draft' | 'generating' | 'completed',
    outline,
    userId: story.user_id.toString(),
    createdAt: story.created_at,
    updatedAt: story.updated_at,
  };
}

export async function getStoryById(id: string, userId: string) {
  const story = await prisma.story.findFirst({
    where: {
      id: BigInt(id),
      user_id: BigInt(userId),
    },
    include: {
      chapters: {
        orderBy: {
          chapter_number: 'asc',
        },
      },
    },
  });

  if (!story) {
    throw new Error('Story not found');
  }

  return story;
}

export async function updateStoryStatus(
  id: string,
  userId: string,
  status: 'draft' | 'generating' | 'completed'
) {
  const story = await prisma.story.update({
    where: {
      id: BigInt(id),
      user_id: BigInt(userId),
    },
    data: {
      status: status as any, // Map to Prisma enum
    },
  });

  return story;
}

export async function addStoryChapter(
  storyId: string,
  userId: string,
  chapter: {
    title: string;
    content: string;
    imageUrl?: string;
    order: number;
  }
) {
  const story = await prisma.story.findFirst({
    where: {
      id: BigInt(storyId),
      user_id: BigInt(userId),
    },
  });

  if (!story) {
    throw new Error('Story not found');
  }

  const newChapter = await prisma.chapter.create({
    data: {
      story_id: BigInt(storyId),
      title: chapter.title,
      content: chapter.content,
      image_url: chapter.imageUrl,
      chapter_number: chapter.order,
    },
  });

  return newChapter;
}