import { PrismaClient } from '@prisma/client';
import { vi, describe, it, expect } from 'vitest';

vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn().mockImplementation(() => ({
    story: {
      create: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
  })),
}));

const prisma = new PrismaClient();

describe('Story Model', () => {
  const userId = 1n;

  it('should create a new story', async () => {
    const storyData = {
      title: 'My First Story',
      user_id: userId,
    };
    (prisma.story.create as any).mockResolvedValue({ id: 1, ...storyData });

    const story = await prisma.story.create({ data: storyData });

    expect(prisma.story.create).toHaveBeenCalledWith({ data: storyData });
    expect(story.title).toBe('My First Story');
  });

  it('should read a story', async () => {
    const storyData = {
      id: 1,
      title: 'Another Story',
      user_id: userId,
    };
    (prisma.story.findUnique as any).mockResolvedValue(storyData);

    const story = await prisma.story.findUnique({ where: { id: 1 } });

    expect(prisma.story.findUnique).toHaveBeenCalledWith({ where: { id: 1 } });
    expect(story?.title).toBe('Another Story');
  });

  it('should update a story', async () => {
    const updatedData = { title: 'Updated Story' };
    (prisma.story.update as any).mockResolvedValue({ id: 1, user_id: userId, ...updatedData });

    const updatedStory = await prisma.story.update({
      where: { id: 1 },
      data: updatedData,
    });

    expect(prisma.story.update).toHaveBeenCalledWith({
      where: { id: 1 },
      data: updatedData,
    });
    expect(updatedStory.title).toBe('Updated Story');
  });

  it('should delete a story', async () => {
    (prisma.story.delete as any).mockResolvedValue({});

    await prisma.story.delete({ where: { id: 1 } });

    expect(prisma.story.delete).toHaveBeenCalledWith({ where: { id: 1 } });
  });
});