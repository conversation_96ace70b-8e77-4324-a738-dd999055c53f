import { PrismaClient } from '@prisma/client';
import { vi, describe, it, expect } from 'vitest';

vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn().mockImplementation(() => ({
    user: {
      create: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
  })),
}));

const prisma = new PrismaClient();

describe('User Model', () => {
  it('should create a new user', async () => {
    const userData = {
      email: '<EMAIL>',
      username: 'testuser',
      password_hash: 'password123',
    };
    (prisma.user.create as any).mockResolvedValue({ id: 1, ...userData });

    const user = await prisma.user.create({ data: userData });

    expect(prisma.user.create).toHaveBeenCalledWith({ data: userData });
    expect(user.email).toBe('<EMAIL>');
  });

  it('should read a user', async () => {
    const userData = {
      id: 1,
      email: '<EMAIL>',
      username: 'testuser',
    };
    (prisma.user.findUnique as any).mockResolvedValue(userData);

    const user = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });

    expect(prisma.user.findUnique).toHaveBeenCalledWith({ where: { email: '<EMAIL>' } });
    expect(user?.username).toBe('testuser');
  });

  it('should update a user', async () => {
    const updatedData = { username: 'updateduser' };
    (prisma.user.update as any).mockResolvedValue({ id: 1, email: '<EMAIL>', ...updatedData });

    const updatedUser = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: updatedData,
    });

    expect(prisma.user.update).toHaveBeenCalledWith({
      where: { email: '<EMAIL>' },
      data: updatedData,
    });
    expect(updatedUser.username).toBe('updateduser');
  });

  it('should delete a user', async () => {
    (prisma.user.delete as any).mockResolvedValue({});

    await prisma.user.delete({ where: { email: '<EMAIL>' } });

    expect(prisma.user.delete).toHaveBeenCalledWith({ where: { email: '<EMAIL>' } });
  });
});