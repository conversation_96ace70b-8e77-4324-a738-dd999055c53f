import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface StoryGenerationParams {
  protagonistName: string;
  protagonistPersonality: string;
  theme: string;
  keyElements: string[];
  style: string;
  ageGroup: string;
  moral: string;
}

export async function generateStoryOutline(params: StoryGenerationParams): Promise<string> {
  const {
    protagonistName,
    protagonistPersonality,
    theme,
    keyElements,
    style,
    ageGroup,
    moral,
  } = params;

  const prompt = `
请为${ageGroup}岁的儿童创作一个${style}风格的童话故事。

故事要求：
- 主角：${protagonistName}，性格特点：${protagonistPersonality}
- 主题：${theme}
- 关键元素：${keyElements.join('、')}
- 故事寓意：${moral}

请生成一个包含以下部分的故事大纲：
1. 故事标题（吸引人且符合主题）
2. 故事背景设定
3. 主要角色介绍
4. 故事开端
5. 故事发展（包含冲突）
6. 故事高潮
7. 故事结局
8. 寓意总结

故事应该：
- 适合${ageGroup}岁儿童的认知水平
- 语言简单易懂，富有想象力
- 情节连贯，有起承转合
- 正面积极，传达正确的价值观
- 字数控制在500-800字左右

请用中文回答，直接提供故事大纲内容。
`;

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "你是一个专业的儿童故事创作助手，擅长为不同年龄段的孩子创作有趣且富有教育意义的童话故事。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.7,
    });

    return completion.choices[0]?.message?.content || "故事大纲生成失败";
  } catch (error) {
    console.error('Error generating story outline:', error);
    throw new Error('Failed to generate story outline');
  }
}

export async function generateStoryChapter(params: {
  outline: string;
  chapterNumber: number;
  previousChapters?: string[];
}): Promise<string> {
  const { outline, chapterNumber, previousChapters = [] } = params;

  const context = previousChapters.length > 0 
    ? `前面的章节内容：\n${previousChapters.join('\n\n')}\n\n` 
    : '';

  const prompt = `
根据以下故事大纲，创作第${chapterNumber}章的内容：

故事大纲：
${outline}

${context}

请创作第${chapterNumber}章的内容，要求：
1. 与故事大纲保持一致
2. 情节连贯，符合逻辑发展
3. 语言生动有趣，适合儿童阅读
4. 每章字数控制在300-500字
5. 结尾要有悬念或自然过渡

请用中文回答，直接提供章节内容。
`;

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "你是一个专业的儿童故事创作助手，擅长创作生动有趣的童话故事章节。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 800,
      temperature: 0.7,
    });

    return completion.choices[0]?.message?.content || "章节内容生成失败";
  } catch (error) {
    console.error('Error generating story chapter:', error);
    throw new Error('Failed to generate story chapter');
  }
}

export async function generateStoryImage(params: {
  sceneDescription: string;
  style: string;
  ageGroup: string;
}): Promise<string> {
  const { sceneDescription, style, ageGroup } = params;

  const prompt = `
为${ageGroup}岁儿童故事创作插图：
场景描述：${sceneDescription}
风格要求：${style}风格，色彩明亮，温馨可爱，适合儿童

要求：
- 画面简洁清晰
- 色彩鲜艳但不过于刺眼
- 符合儿童审美
- 避免恐怖或暴力元素
`;

  try {
    const response = await openai.images.generate({
      model: "dall-e-3",
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "standard",
      style: "vivid",
    });

    return response.data?.[0]?.url || '';
  } catch (error) {
    console.error('Error generating story image:', error);
    throw new Error('Failed to generate story image');
  }
}