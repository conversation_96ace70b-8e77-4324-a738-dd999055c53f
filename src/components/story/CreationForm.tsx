'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useStoryCreationStore } from '@/store/story-creation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, Upload, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

const formSchema = z.object({
  protagonistName: z.string().min(1, '主角姓名不能为空'),
  protagonistPersonality: z.string().min(1, '主角性格不能为空'),
  protagonistImage: z.any().optional(), // 在客户端会是File类型
  theme: z.string().min(1, '故事主题不能为空'),
  keyElements: z.array(z.string()).min(1, '至少需要一个关键元素'),
  style: z.string().min(1, '故事风格不能为空'),
  ageGroup: z.string().min(1, '请选择面向年龄'),
  moral: z.string().min(1, '故事寓意不能为空'),
});

type FormData = z.infer<typeof formSchema>;

const ageGroups = [
  { value: '3-6', label: '3-6岁' },
  { value: '7-9', label: '7-9岁' },
  { value: '10-12', label: '10-12岁' },
  { value: '13+', label: '13岁以上' },
];

const storyStyles = [
  { value: '温馨', label: '温馨治愈' },
  { value: '冒险', label: '冒险刺激' },
  { value: '幽默', label: '幽默有趣' },
  { value: '神秘', label: '神秘奇幻' },
  { value: '教育', label: '寓教于乐' },
];

const storyThemes = [
  { value: '友谊', label: '友谊' },
  { value: '勇气', label: '勇气' },
  { value: '诚实', label: '诚实' },
  { value: '分享', label: '分享' },
  { value: '环保', label: '环保' },
  { value: '家庭', label: '家庭' },
  { value: '成长', label: '成长' },
  { value: '梦想', label: '梦想' },
];

export function CreationForm() {
  const { formData, setFormData, createStory, isGenerating } = useStoryCreationStore();
  const [keyElementInput, setKeyElementInput] = useState('');
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      protagonistName: formData.protagonistName || '',
      protagonistPersonality: formData.protagonistPersonality || '',
      theme: formData.theme || '',
      keyElements: formData.keyElements || [],
      style: formData.style || '',
      ageGroup: formData.ageGroup || '3-6',
      moral: formData.moral || '',
    },
  });

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        form.setError('protagonistImage', {
          type: 'manual',
          message: '图片大小不能超过5MB',
        });
        return;
      }

      if (!file.type.startsWith('image/')) {
        form.setError('protagonistImage', {
          type: 'manual',
          message: '请上传图片文件',
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = (event) => {
        setPreviewImage(event.target?.result as string);
      };
      reader.readAsDataURL(file);
      
      form.setValue('protagonistImage', file);
      setFormData({ protagonistImage: file });
    }
  };

  const addKeyElement = () => {
    const currentElements = form.getValues('keyElements');
    if (keyElementInput.trim() && !currentElements.includes(keyElementInput.trim())) {
      const newElements = [...currentElements, keyElementInput.trim()];
      form.setValue('keyElements', newElements);
      setFormData({ keyElements: newElements });
      setKeyElementInput('');
    }
  };

  const removeKeyElement = (element: string) => {
    const currentElements = form.getValues('keyElements');
    const newElements = currentElements.filter(e => e !== element);
    form.setValue('keyElements', newElements);
    setFormData({ keyElements: newElements });
  };

  const onSubmit = async (data: FormData) => {
    try {
      await createStory(data);
    } catch (error) {
      console.error('创建故事失败:', error);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>创作新故事</CardTitle>
        <CardDescription>
          填写以下信息，让AI为您创作一个独特的童话故事
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 主角设定 */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">主角设定</h3>
              
              <FormField
                control={form.control}
                name="protagonistName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>主角姓名</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：小红、小明" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="protagonistPersonality"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>主角性格</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：勇敢、善良、聪明" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="protagonistImage"
                render={({ field: { value, onChange, ...field } }) => (
                  <FormItem>
                    <FormLabel>主角参考图片（可选）</FormLabel>
                    <FormControl>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Input
                            type="file"
                            accept="image/*"
                            onChange={handleImageChange}
                            className="cursor-pointer"
                            {...field}
                          />
                          <Upload className="h-4 w-4 text-gray-500" />
                        </div>
                        {previewImage && (
                          <div className="relative">
                            <img
                              src={previewImage}
                              alt="预览"
                              className="w-32 h-32 object-cover rounded-md"
                            />
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormDescription>
                      上传一张图片作为主角的参考形象
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 故事主题 */}
            <FormField
              control={form.control}
              name="theme"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>故事主题</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择一个主题" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {storyThemes.map((theme) => (
                        <SelectItem key={theme.value} value={theme.value}>
                          {theme.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 关键元素 */}
            <FormField
              control={form.control}
              name="keyElements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>关键元素</FormLabel>
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <Input
                        value={keyElementInput}
                        onChange={(e) => setKeyElementInput(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            addKeyElement();
                          }
                        }}
                        placeholder="添加关键元素"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={addKeyElement}
                      >
                        添加
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {field.value?.map((element) => (
                        <Badge
                          key={element}
                          variant="secondary"
                          className="cursor-pointer"
                          onClick={() => removeKeyElement(element)}
                        >
                          {element}
                          <X className="ml-1 h-3 w-3" />
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 故事风格 */}
            <FormField
              control={form.control}
              name="style"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>故事风格</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择故事风格" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {storyStyles.map((style) => (
                        <SelectItem key={style.value} value={style.value}>
                          {style.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 面向年龄 */}
            <FormField
              control={form.control}
              name="ageGroup"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>面向年龄</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择年龄组" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ageGroups.map((age) => (
                        <SelectItem key={age.value} value={age.value}>
                          {age.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 故事寓意 */}
            <FormField
              control={form.control}
              name="moral"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>故事寓意</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="希望故事传达什么道理或价值观？"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full"
              disabled={isGenerating}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  AI正在创作中...
                </>
              ) : (
                '开始创作'
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}