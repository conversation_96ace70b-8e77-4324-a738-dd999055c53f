import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { CreationForm } from '../CreationForm';
import { useStoryCreationStore } from '@/store/story-creation';

// Mock the store
vi.mock('@/store/story-creation', () => ({
  useStoryCreationStore: vi.fn(),
}));

describe('CreationForm', () => {
  const mockSetFormData = vi.fn();
  const mockSetCurrentStep = vi.fn();
  const mockCreateStory = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useStoryCreationStore as any).mockReturnValue({
      formData: {},
      setFormData: mockSetFormData,
      setCurrentStep: mockSetCurrentStep,
      createStory: mockCreateStory,
    });
  });

  it('should render all form fields', () => {
    render(<CreationForm />);
    
    expect(screen.getByLabelText(/主角姓名/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/主角性格/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/故事主题/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/关键元素/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/故事风格/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/面向年龄/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/故事寓意/i)).toBeInTheDocument();
  });

  it('should handle form input changes', async () => {
    render(<CreationForm />);
    
    const nameInput = screen.getByLabelText(/主角姓名/i);
    fireEvent.change(nameInput, { target: { value: '小明' } });
    
    await waitFor(() => {
      expect(mockSetFormData).toHaveBeenCalledWith({ protagonistName: '小明' });
    });
  });

  it('should validate required fields', async () => {
    render(<CreationForm />);
    
    const submitButton = screen.getByRole('button', { name: /开始创作/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/主角姓名不能为空/i)).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    const mockFormData = {
      protagonistName: '小红',
      protagonistPersonality: '勇敢',
      theme: '友谊',
      keyElements: ['魔法森林', '神秘生物'],
      style: '温馨',
      ageGroup: '3-6',
      moral: '友谊的力量',
    };
    
    (useStoryCreationStore as any).mockReturnValue({
      formData: mockFormData,
      setFormData: mockSetFormData,
      setCurrentStep: mockSetCurrentStep,
      createStory: mockCreateStory,
    });

    render(<CreationForm />);
    
    const submitButton = screen.getByRole('button', { name: /开始创作/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockCreateStory).toHaveBeenCalledWith(mockFormData);
    });
  });

  it('should handle image upload', async () => {
    render(<CreationForm />);
    
    const file = new File(['test'], 'test.png', { type: 'image/png' });
    const input = screen.getByLabelText(/主角参考图片/i);
    
    fireEvent.change(input, { target: { files: [file] } });
    
    await waitFor(() => {
      expect(mockSetFormData).toHaveBeenCalled();
    });
  });
});