import { render, screen, fireEvent } from '@testing-library/react';
import { RegisterForm } from '../RegisterForm';
import { describe, it, expect, vi } from 'vitest';

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}));

describe('RegisterForm', () => {
  it('should render all form fields', () => {
    render(<RegisterForm />);
    expect(screen.getByLabelText(/username/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /register/i })).toBeInTheDocument();
  });

  it('should show validation errors for empty fields', async () => {
    render(<RegisterForm />);
    fireEvent.click(screen.getByRole('button', { name: /register/i }));

    expect(await screen.findByText(/Username must be at least 3 characters./i)).toBeInTheDocument();
    expect(await screen.findByText(/Invalid email address./i)).toBeInTheDocument();
    expect(await screen.findByText(/Password must be at least 6 characters./i)).toBeInTheDocument();
  });
});