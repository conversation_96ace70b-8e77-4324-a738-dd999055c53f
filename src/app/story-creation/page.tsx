'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CreationForm } from '@/components/story/CreationForm';
import { useStoryCreationStore } from '@/store/story-creation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, BookOpen } from 'lucide-react';

export default function StoryCreationPage() {
  const router = useRouter();
  const { currentStep, reset } = useStoryCreationStore();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    // 检查用户是否已登录
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/me');
        if (response.ok) {
          setIsAuthenticated(true);
        } else {
          setIsAuthenticated(false);
          router.push('/login');
        }
      } catch (error) {
        setIsAuthenticated(false);
        router.push('/login');
      }
    };

    checkAuth();
  }, [router]);

  useEffect(() => {
    // 重置状态当页面加载时
    reset();
  }, [reset]);

  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      <div className="container mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => router.push('/')}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回首页
          </Button>
          
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <BookOpen className="h-12 w-12 text-blue-500 mr-3" />
              <h1 className="text-4xl font-bold text-gray-900">创作童话故事</h1>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              让我们一起创造一个充满想象力的童话世界。填写下面的表单，AI将为您创作一个独特的故事。
            </p>
          </div>
        </div>

        {/* 创作进度指示器 */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            <div className={cn(
              "flex items-center space-x-2",
              currentStep >= 0 && "text-blue-600"
            )}>
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center",
                currentStep >= 0 ? "bg-blue-600 text-white" : "bg-gray-300"
              )}>
                1
              </div>
              <span className="text-sm font-medium">填写信息</span>
            </div>
            
            <div className="w-24 h-1 bg-gray-300 rounded">
              <div 
                className="h-full bg-blue-600 rounded transition-all duration-300"
                style={{ width: currentStep >= 1 ? '100%' : '0%' }}
              />
            </div>
            
            <div className={cn(
              "flex items-center space-x-2",
              currentStep >= 1 && "text-blue-600"
            )}>
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center",
                currentStep >= 1 ? "bg-blue-600 text-white" : "bg-gray-300"
              )}>
                2
              </div>
              <span className="text-sm font-medium">生成故事</span>
            </div>
            
            <div className="w-24 h-1 bg-gray-300 rounded">
              <div 
                className="h-full bg-blue-600 rounded transition-all duration-300"
                style={{ width: currentStep >= 2 ? '100%' : '0%' }}
              />
            </div>
            
            <div className={cn(
              "flex items-center space-x-2",
              currentStep >= 2 && "text-blue-600"
            )}>
              <div className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center",
                currentStep >= 2 ? "bg-blue-600 text-white" : "bg-gray-300"
              )}>
                3
              </div>
              <span className="text-sm font-medium">完成创作</span>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="max-w-4xl mx-auto">
          {currentStep === 0 && (
            <CreationForm />
          )}
          
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle>正在生成故事</CardTitle>
                <CardDescription>
                  AI正在根据您提供的信息创作故事，请稍候...
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle>故事创作完成！</CardTitle>
                <CardDescription>
                  您的童话故事已经创作完成，可以开始阅读了。
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <BookOpen className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <p className="text-lg text-gray-600 mb-6">
                    恭喜！您的童话故事已经创作完成。
                  </p>
                  <Button onClick={() => router.push('/stories')}>
                    查看我的故事
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 创作提示 */}
        <div className="mt-12 max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>创作小贴士</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• 主角姓名可以是中文名、英文名或创意名字</li>
                <li>• 关键元素可以是地点、物品、角色或特殊能力</li>
                <li>• 故事寓意应该简单明了，适合儿童理解</li>
                <li>• 不同年龄段的孩子适合不同复杂度的故事</li>
                <li>• 您可以随时重新创作，每次都会得到不同的故事</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

// 辅助函数
function cn(...classes: (string | undefined | null | boolean)[]) {
  return classes.filter(Boolean).join(' ');
}