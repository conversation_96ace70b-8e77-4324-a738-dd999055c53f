import { describe, it, expect, afterAll } from 'vitest';
import request from 'supertest';
import { createServer } from 'http';
import { POST as registerHandler } from '../register/route';
import { POST as loginHandler } from '../login/route';
import { POST as logoutHandler } from '../logout/route';
import { GET as meHandler } from '../me/route';
import { prisma } from '@/lib/prisma';

// Helper to create a test server
const createTestServer = (handler: any) => {
  return createServer((req, res) => {
    const chunks: any[] = [];
    req.on('data', chunk => {
        chunks.push(chunk);
    });
    req.on('end', async () => {
        const body = Buffer.concat(chunks);
        const nextRequest = new Request(`http://localhost${req.url}`, {
            method: req.method,
            headers: new Headers(req.headers as HeadersInit),
            body: body.length > 0 ? body : null,
        });

        const response = await handler(nextRequest);
        res.statusCode = response.status;
        for (const [key, value] of response.headers.entries()) {
            res.setHeader(key, value);
        }
        const responseBody = await response.text();
        res.end(responseBody);
    });
  });
};

describe('Auth API', () => {
  let server: ReturnType<typeof createTestServer>;

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
        const user = {
            username: `testuser_${Date.now()}`,
            email: `test_${Date.now()}@example.com`,
            password: 'password123',
        };

        const handler = (req: any) => registerHandler(req);
        server = createTestServer(handler);
        
        const res = await request(server)
            .post('/api/auth/register')
            .send(user)
            .set('Content-Type', 'application/json');

        expect(res.status).toBe(201);
        const body = JSON.parse(res.text);
        expect(body).toHaveProperty('user');
        expect(body).toHaveProperty('token');
        expect(body.user.email).toBe(user.email);
    });
  });
  
  describe('POST /api/auth/login', () => {
    it('should login an existing user', async () => {
        const user = {
            username: `testuser_${Date.now()}`,
            email: `test_${Date.now()}@example.com`,
            password: 'password123',
        };

        // First, register the user
        const registerHandlerFn = (req: any) => registerHandler(req);
        server = createTestServer(registerHandlerFn);
        await request(server).post('/api/auth/register').send(user);

        // Then, login
        const loginHandlerFn = (req: any) => loginHandler(req);
        server = createTestServer(loginHandlerFn);
        const res = await request(server)
            .post('/api/auth/login')
            .send({ email: user.email, password: user.password });

        expect(res.status).toBe(200);
        const body = JSON.parse(res.text);
        expect(body).toHaveProperty('user');
        expect(body.user.email).toBe(user.email);
        expect(res.headers['set-cookie']).toBeDefined();
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout a user', async () => {
        const handler = () => logoutHandler();
        server = createTestServer(handler);
        const res = await request(server).post('/api/auth/logout');

        expect(res.status).toBe(200);
        expect(res.headers['set-cookie']).toBeDefined();
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return the current user', async () => {
        const user = {
            username: `testuser_${Date.now()}`,
            email: `test_${Date.now()}@example.com`,
            password: 'password123',
        };

        // Register and login to get a token
        const registerHandlerFn = (req: any) => registerHandler(req);
        server = createTestServer(registerHandlerFn);
        const registerRes = await request(server).post('/api/auth/register').send(user);
        const registerBody = JSON.parse(registerRes.text);
        const token = registerBody.token;

        // Get current user
        const meHandlerFn = (req: any) => meHandler(req);
        server = createTestServer(meHandlerFn);
        const res = await request(server)
            .get('/api/auth/me')
            .set('Authorization', `Bearer ${token}`);

        expect(res.status).toBe(200);
        const body = JSON.parse(res.text);
        expect(body).toHaveProperty('user');
        expect(body.user.email).toBe(user.email);
    });
  });
});