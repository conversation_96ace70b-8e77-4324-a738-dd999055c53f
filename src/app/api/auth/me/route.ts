import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';

interface DecodedToken {
    userId: string;
}

export async function GET(req: NextRequest) {
    try {
        let token;
        if (process.env.NODE_ENV === 'test') {
            token = req.headers.get('Authorization')?.split(' ')[1];
        } else {
            token = req.cookies.get('token')?.value;
        }

        if (!token) {
            return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as DecodedToken;

        const user = await prisma.user.findUnique({
            where: { id: BigInt(decoded.userId) },
            select: {
                id: true,
                username: true,
                email: true,
                created_at: true,
                updated_at: true,
            },
        });

        if (!user) {
            return NextResponse.json({ message: 'User not found' }, { status: 404 });
        }

        const serializableUser = {
            ...user,
            id: user.id.toString(),
        };

        return NextResponse.json({ user: serializableUser });
    } catch (error) {
        console.error('Get current user error:', error);
        if (error instanceof jwt.JsonWebTokenError) {
            return NextResponse.json({ message: 'Invalid token' }, { status: 401 });
        }
        return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
    }
}