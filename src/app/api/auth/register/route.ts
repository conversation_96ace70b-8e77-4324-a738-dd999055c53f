import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';

const registerSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters long'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters long'),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const validation = registerSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json({ errors: validation.error.flatten().fieldErrors }, { status: 400 });
    }

    const { username, email, password } = validation.data;

    const existingUserByEmail = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUserByEmail) {
      return NextResponse.json({ message: 'User with this email already exists' }, { status: 409 });
    }
    
    const existingUserByUsername = await prisma.user.findUnique({
        where: { username },
    });

    if (existingUserByUsername) {
        return NextResponse.json({ message: 'User with this username already exists' }, { status: 409 });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const user = await prisma.user.create({
      data: {
        username,
        email,
        password_hash: hashedPassword,
      },
    });

    const token = jwt.sign({ userId: user.id.toString() }, process.env.JWT_SECRET!, {
      expiresIn: '1h',
    });

    // Exclude password from the returned user object
    const { password_hash: _, ...userWithoutPassword } = user;
    
    const serializableUser = {
        ...userWithoutPassword,
        id: userWithoutPassword.id.toString(),
    };

    return NextResponse.json({ user: serializableUser, token }, { status: 201 });
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}