import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/prisma';
import { createStory } from '@/services/story-service';

const createStorySchema = z.object({
  protagonistName: z.string().min(1, '主角姓名不能为空'),
  protagonistPersonality: z.string().min(1, '主角性格不能为空'),
  theme: z.string().min(1, '故事主题不能为空'),
  keyElements: z.array(z.string()).min(1, '至少需要一个关键元素'),
  style: z.string().min(1, '故事风格不能为空'),
  ageGroup: z.string().min(1, '请选择面向年龄'),
  moral: z.string().min(1, '故事寓意不能为空'),
});

// 认证辅助函数
async function authenticateUser(request: NextRequest) {
  const token = request.cookies.get('token')?.value;

  if (!token) {
    return null;
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: string };
    return decoded.userId;
  } catch (error) {
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const userId = await authenticateUser(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 解析和验证请求体
    const body = await request.json();
    const validatedData = createStorySchema.parse(body);

    // 创建故事
    const story = await createStory({
      ...validatedData,
      userId: userId,
    });

    return NextResponse.json(story, { status: 201 });
  } catch (error) {
    console.error('Error creating story:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const userId = await authenticateUser(request);
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    const skip = (page - 1) * limit;

    const where: any = {
      user_id: BigInt(userId),
      ...(status && { status: status as any }),
    };

    const [stories, total] = await Promise.all([
      prisma.story.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          created_at: 'desc',
        },
        select: {
          id: true,
          title: true,
          description: true,
          story_data: true,
          status: true,
          created_at: true,
          updated_at: true,
        },
      }),
      prisma.story.count({ where }),
    ]);

    // 转换数据格式
    const formattedStories = stories.map(story => {
      const storyData = story.story_data as any;
      return {
        id: story.id.toString(),
        title: story.title,
        protagonistName: storyData?.protagonistName || '',
        theme: storyData?.theme || '',
        status: story.status,
        createdAt: story.created_at,
        updatedAt: story.updated_at,
      };
    });

    return NextResponse.json({
      stories: formattedStories,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching stories:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}