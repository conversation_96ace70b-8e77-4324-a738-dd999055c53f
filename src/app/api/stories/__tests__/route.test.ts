import { describe, it, expect, vi, beforeEach } from 'vitest';
import { POST } from '../route';
import { createStory } from '@/services/story-service';
import { getServerSession } from 'next-auth';
import { NextRequest } from 'next/server';

// Mock dependencies
vi.mock('@/services/story-service', () => ({
  createStory: vi.fn(),
}));

vi.mock('next-auth', () => ({
  getServerSession: vi.fn(),
}));

describe('POST /api/stories', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create a new story with valid data', async () => {
    const mockSession = { user: { id: 'user123', email: '<EMAIL>' } };
    (getServerSession as any).mockResolvedValue(mockSession);

    const mockStory = {
      id: 'story123',
      title: '小红帽的新冒险',
      protagonistName: '小红',
      theme: '友谊',
      status: 'draft',
      userId: 'user123',
    };
    (createStory as any).mockResolvedValue(mockStory);

    const request = new NextRequest('http://localhost:3000/api/stories', {
      method: 'POST',
      body: JSON.stringify({
        protagonistName: '小红',
        protagonistPersonality: '勇敢',
        theme: '友谊',
        keyElements: ['魔法森林', '神秘生物'],
        style: '温馨',
        ageGroup: '3-6',
        moral: '友谊的力量',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(201);
    expect(data).toEqual(mockStory);
    expect(createStory).toHaveBeenCalledWith({
      protagonistName: '小红',
      protagonistPersonality: '勇敢',
      theme: '友谊',
      keyElements: ['魔法森林', '神秘生物'],
      style: '温馨',
      ageGroup: '3-6',
      moral: '友谊的力量',
      userId: 'user123',
    });
  });

  it('should return 401 if user is not authenticated', async () => {
    (getServerSession as any).mockResolvedValue(null);

    const request = new NextRequest('http://localhost:3000/api/stories', {
      method: 'POST',
      body: JSON.stringify({
        protagonistName: '小红',
        theme: '友谊',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.error).toBe('Unauthorized');
  });

  it('should return 400 for invalid data', async () => {
    const mockSession = { user: { id: 'user123', email: '<EMAIL>' } };
    (getServerSession as any).mockResolvedValue(mockSession);

    const request = new NextRequest('http://localhost:3000/api/stories', {
      method: 'POST',
      body: JSON.stringify({
        // Missing required fields
        theme: '友谊',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toContain('Validation error');
  });

  it('should handle server errors', async () => {
    const mockSession = { user: { id: 'user123', email: '<EMAIL>' } };
    (getServerSession as any).mockResolvedValue(mockSession);

    (createStory as any).mockRejectedValue(new Error('AI service error'));

    const request = new NextRequest('http://localhost:3000/api/stories', {
      method: 'POST',
      body: JSON.stringify({
        protagonistName: '小红',
        protagonistPersonality: '勇敢',
        theme: '友谊',
        keyElements: ['魔法森林'],
        style: '温馨',
        ageGroup: '3-6',
        moral: '友谊的力量',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Internal server error');
  });
});