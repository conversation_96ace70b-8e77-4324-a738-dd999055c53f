import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Users } from "lucide-react"
import Link from "next/link"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50">
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BookOpen className="h-8 w-8 text-purple-600" />
            <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              AI童话世界
            </span>
          </div>
          <nav className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost">登录</Button>
            </Link>
            <Link href="/register">
              <Button>开始创作</But<PERSON>>
            </Link>
          </nav>
        </div>
      </header>

      <main className="container mx-auto px-4 py-16">
        <div className="text-center space-y-8">
          <div className="space-y-4">
            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent">
              创造属于你的童话故事
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              使用AI技术，将孩子的想象力变成精美的童话故事书。每一页都充满魔法与奇迹。
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/story-creation">
              <Button size="lg" className="text-lg">
                <Sparkles className="mr-2 h-5 w-5" />
                开始创作
              </Button>
            </Link>
            <Link href="/community">
              <Button size="lg" variant="outline" className="text-lg">
                <Users className="mr-2 h-5 w-5" />
                探索社区
              </Button>
            </Link>
          </div>
        </div>

        <div className="mt-20 grid md:grid-cols-3 gap-8">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
              <Sparkles className="h-8 w-8 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold">AI智能创作</h3>
            <p className="text-gray-600">
              只需输入简单的想法，AI就能生成完整的故事情节和精美插图
            </p>
          </div>

          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto">
              <BookOpen className="h-8 w-8 text-pink-600" />
            </div>
            <h3 className="text-xl font-semibold">个性化定制</h3>
            <p className="text-gray-600">
              根据孩子的年龄、兴趣和喜好，定制专属的童话故事
            </p>
          </div>

          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold">社区分享</h3>
            <p className="text-gray-600">
              与全球家庭分享你的创作，发现更多精彩的童话故事
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}
