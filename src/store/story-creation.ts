import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface StoryFormData {
  protagonistName: string;
  protagonistPersonality: string;
  protagonistImage?: File;
  theme: string;
  keyElements: string[];
  style: string;
  ageGroup: string;
  moral: string;
}

export interface StoryChapter {
  id: string;
  title: string;
  content: string;
  imageUrl?: string;
  order: number;
}

export interface StoryCreationState {
  // Form data
  formData: StoryFormData;
  
  // Story generation
  currentStep: number;
  isGenerating: boolean;
  generatedOutline?: string;
  chapters: StoryChapter[];
  
  // Error handling
  error?: string;
  
  // Actions
  setFormData: (data: Partial<StoryFormData>) => void;
  setCurrentStep: (step: number) => void;
  setGenerating: (generating: boolean) => void;
  setGeneratedOutline: (outline: string) => void;
  setChapters: (chapters: StoryChapter[]) => void;
  setError: (error?: string) => void;
  createStory: (formData: StoryFormData) => Promise<void>;
  reset: () => void;
}

const initialFormData: StoryFormData = {
  protagonistName: '',
  protagonistPersonality: '',
  theme: '',
  keyElements: [],
  style: '',
  ageGroup: '3-6',
  moral: '',
};

const useStoryCreationStore = create<StoryCreationState>()(
  devtools(
    (set, get) => ({
      formData: initialFormData,
      currentStep: 0,
      isGenerating: false,
      chapters: [],
      
      setFormData: (data) =>
        set((state) => ({
          formData: { ...state.formData, ...data },
        })),
      
      setCurrentStep: (step) =>
        set(() => ({
          currentStep: step,
        })),
      
      setGenerating: (generating) =>
        set(() => ({
          isGenerating: generating,
        })),
      
      setGeneratedOutline: (outline) =>
        set(() => ({
          generatedOutline: outline,
        })),
      
      setChapters: (chapters) =>
        set(() => ({
          chapters,
        })),
      
      setError: (error) =>
        set(() => ({
          error,
        })),
      
      createStory: async (formData: StoryFormData) => {
        set({ isGenerating: true, error: undefined });
        
        try {
          const response = await fetch('/api/stories', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData),
          });
          
          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '创建故事失败');
          }
          
          const story = await response.json();
          set({ 
            formData: { ...get().formData, ...formData },
            currentStep: 1,
            isGenerating: false,
          });
          
          return story;
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '创建故事失败',
            isGenerating: false,
          });
          throw error;
        }
      },
      
      reset: () =>
        set({
          formData: initialFormData,
          currentStep: 0,
          isGenerating: false,
          generatedOutline: undefined,
          chapters: [],
          error: undefined,
        }),
    }),
    {
      name: 'story-creation-store',
    }
  )
);

export { useStoryCreationStore };