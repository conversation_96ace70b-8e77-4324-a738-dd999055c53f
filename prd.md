# AI童话故事书 - 产品需求文档 (PRD)

## 1. 产品愿景与目标用户

### 1.1. 愿景
成为全球领先的个性化AI故事内容生成与分享平台，激发每个人的创造力，让亲子阅读充满更多乐趣与想象。

### 1.2. 目标用户画像
*   **核心用户:** 3-10岁儿童的父母，希望为孩子提供独特、有教育意义的睡前故事，并参与到创作过程中。
*   **次要用户:** 教育工作者、儿童文学爱好者、以及希望通过故事创作来表达自己的青少年。

---

## 2. 核心功能模块：AI故事创作引擎

### 2.1. 第一步：灵感注入 (引导式创意输入)
*   **智能表单:** 提供结构化的输入界面，而非单一文本框。用户需填写：
    *   **主角设定:** 姓名、是小动物还是人类、三个主要性格特质（如勇敢、好奇、善良）。支持上传一张参考图片以影响角色形象。
    *   **故事主题:** 从预设标签中选择（如“冒险”、“友谊”、“学会分享”、“克服恐惧”），或自定义输入。
    *   **关键元素:** 故事中必须出现的2-3个物品或场景（如“魔法森林”、“会飞的鞋子”）。
    *   **故事风格与寓意:** 选择故事的基调（如“温馨治愈”、“幽默搞笑”）并简述希望传达的道理。
    *   **面向年龄:** 选择（3-5岁，6-8岁，9-12岁），AI将据此调整用词复杂度和故事情节深度。

### 2.2. 第二步：AI协作生成 (人机交互式创作)
*   **章节式生成:** AI首先生成故事大纲（开端、发展、高潮、结局）供用户确认。确认后，AI逐页或逐章节生成详细文本。
*   **交互式编辑:** 用户在AI生成每一页后，可以进行实时修改、润色文本，或给出反馈（如“让这里更紧张一点”），AI会基于反馈重写该段落。
*   **插画生成与风格选择:**
    *   **同步生成:** 每生成一页文本，AI自动解析内容，生成与之匹配的插画。
    *   **风格锁定:** 用户在开始时选择一种全局绘画风格（如“皮克斯3D动画风”、“日式水彩”、“复古绘本”），确保全书风格统一。
    *   **角色一致性:** 运用先进的角色锁定（Character Consistency）技术，确保主角在不同场景、不同动作下的形象保持高度一致。
    *   **图像微调:** 提供“重绘此图”按钮，并允许用户输入简单的指令进行微调（如“给主角加上一顶帽子”）。

### 2.3. 第三步：预览与发布 (成品与分享)
*   **沉浸式预览:** 以精美的电子书翻页效果进行全流程预览。
*   **一键排版:** 自动进行专业的图文排版，生成封面（包含书名和作者名）。
*   **多种导出与分享格式:** 支持导出为PDF文件、长图、或生成专属网页链接，方便分享至社交媒体或家庭群组。

---

## 3. UGC社区生态模块

*   **个人书架:** 用户的私人图书馆，分类管理“我的创作”和“我的收藏”。
*   **发现广场:**
    *   **推荐流:** 基于算法，向用户推荐其他创作者的优质公开故事。
    *   **排行榜:** 设立“本周热门”、“新星作者”等榜单，激励创作。
    *   **专题活动:** 定期举办主题创作活动（如“圣诞节奇遇记”），并提供官方模板和素材。
*   **互动系统:** 用户可以对公开的故事进行点赞、评论、收藏，并关注自己喜爱的创作者。
*   **用户主页:** 展示创作者的个人简介、作品集、获得的赞与关注数。

---

## 4. 商业模式

*   **Freemium (免费增值)模型:**
    *   **免费版:** 每月可免费创作1-2本故事（有功能限制，如画风选择较少、有广告、导出有水印）。
    *   **订阅会员 (Pro):**
        *   无限次创作。
        *   解锁所有高级绘画风格和独家模板。
        *   享受角色一致性增强、更高清的图像生成与导出权限。
        *   无广告，并可将故事设置为私密。
        *   未来可拓展：实体书打印服务接口。

---

## 5. 非功能性需求

*   **UI/UX设计:** 界面需简洁、直观、色彩明快，富有童趣和想象力，操作流程引导清晰，降低创作门槛。
*   **性能要求:** 故事和图像的生成过程需有明确的进度反馈，整体生成时间（一本10页故事）应控制在90秒以内。
*   **技术栈考量:** 需整合先进的LLM（用于故事生成与改写）和Text-to-Image模型（用于插画生成），并重点攻克角色一致性技术难题。
*   **内容审核:** 建立严格的关键词过滤和AI内容审核机制，确保所有生成内容健康、安全、适合儿童。