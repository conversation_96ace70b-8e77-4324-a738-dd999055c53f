-- CreateEnum
CREATE TYPE "public"."Role" AS ENUM ('user', 'admin', 'moderator');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."PlanType" AS ENUM ('free', 'pro', 'premium');

-- CreateEnum
CREATE TYPE "public"."StoryStatus" AS ENUM ('draft', 'published', 'archived');

-- CreateEnum
CREATE TYPE "public"."Visibility" AS ENUM ('public', 'private', 'unlisted');

-- CreateEnum
CREATE TYPE "public"."IllustrationStatus" AS ENUM ('pending', 'processing', 'completed', 'failed');

-- CreateEnum
CREATE TYPE "public"."AIGenerationType" AS ENUM ('story', 'chapter', 'illustration', 'character');

-- CreateEnum
CREATE TYPE "public"."AIGenerationStatus" AS ENUM ('pending', 'processing', 'completed', 'failed');

-- CreateTable
CREATE TABLE "public"."User" (
    "id" BIGSERIAL NOT NULL,
    "uuid" UUID NOT NULL DEFAULT gen_random_uuid(),
    "email" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "password_hash" TEXT NOT NULL,
    "avatar_url" TEXT,
    "bio" TEXT,
    "role" "public"."Role" NOT NULL DEFAULT 'user',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_login" TIMESTAMP(3),
    "is_active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Subscription" (
    "id" BIGSERIAL NOT NULL,
    "user_id" BIGINT NOT NULL,
    "plan_type" "public"."PlanType" NOT NULL,
    "price" DECIMAL(10,2),
    "start_date" TIMESTAMP(3) NOT NULL,
    "end_date" TIMESTAMP(3),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Subscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Story" (
    "id" BIGSERIAL NOT NULL,
    "user_id" BIGINT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "cover_image" TEXT,
    "story_data" JSONB,
    "status" "public"."StoryStatus" NOT NULL DEFAULT 'draft',
    "visibility" "public"."Visibility" NOT NULL DEFAULT 'public',
    "view_count" INTEGER NOT NULL DEFAULT 0,
    "like_count" INTEGER NOT NULL DEFAULT 0,
    "comment_count" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "published_at" TIMESTAMP(3),

    CONSTRAINT "Story_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Chapter" (
    "id" BIGSERIAL NOT NULL,
    "story_id" BIGINT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT,
    "chapter_number" INTEGER NOT NULL,
    "image_url" TEXT,
    "chapter_data" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Chapter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Illustration" (
    "id" BIGSERIAL NOT NULL,
    "story_id" BIGINT NOT NULL,
    "chapter_id" BIGINT,
    "image_url" TEXT NOT NULL,
    "prompt" TEXT,
    "style" TEXT,
    "image_data" JSONB,
    "status" "public"."IllustrationStatus" NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Illustration_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Like" (
    "id" BIGSERIAL NOT NULL,
    "user_id" BIGINT NOT NULL,
    "story_id" BIGINT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Like_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Comment" (
    "id" BIGSERIAL NOT NULL,
    "user_id" BIGINT NOT NULL,
    "story_id" BIGINT NOT NULL,
    "content" TEXT NOT NULL,
    "parent_id" BIGINT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Comment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."Follow" (
    "id" BIGSERIAL NOT NULL,
    "follower_id" BIGINT NOT NULL,
    "following_id" BIGINT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Follow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."AI_Generation" (
    "id" BIGSERIAL NOT NULL,
    "user_id" BIGINT NOT NULL,
    "type" "public"."AIGenerationType" NOT NULL,
    "prompt" JSONB NOT NULL,
    "response" JSONB,
    "status" "public"."AIGenerationStatus" NOT NULL DEFAULT 'pending',
    "error_message" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP(3),

    CONSTRAINT "AI_Generation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_uuid_key" ON "public"."User"("uuid");

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "public"."User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_username_key" ON "public"."User"("username");

-- CreateIndex
CREATE INDEX "Story_user_id_idx" ON "public"."Story"("user_id");

-- CreateIndex
CREATE INDEX "Story_status_idx" ON "public"."Story"("status");

-- CreateIndex
CREATE INDEX "Story_visibility_idx" ON "public"."Story"("visibility");

-- CreateIndex
CREATE INDEX "Story_created_at_idx" ON "public"."Story"("created_at");

-- CreateIndex
CREATE INDEX "Chapter_story_id_idx" ON "public"."Chapter"("story_id");

-- CreateIndex
CREATE INDEX "Chapter_story_id_chapter_number_idx" ON "public"."Chapter"("story_id", "chapter_number");

-- CreateIndex
CREATE INDEX "Illustration_story_id_idx" ON "public"."Illustration"("story_id");

-- CreateIndex
CREATE INDEX "Illustration_chapter_id_idx" ON "public"."Illustration"("chapter_id");

-- CreateIndex
CREATE INDEX "Illustration_status_idx" ON "public"."Illustration"("status");

-- CreateIndex
CREATE INDEX "Like_user_id_idx" ON "public"."Like"("user_id");

-- CreateIndex
CREATE INDEX "Like_story_id_idx" ON "public"."Like"("story_id");

-- CreateIndex
CREATE UNIQUE INDEX "Like_user_id_story_id_key" ON "public"."Like"("user_id", "story_id");

-- CreateIndex
CREATE INDEX "Comment_user_id_idx" ON "public"."Comment"("user_id");

-- CreateIndex
CREATE INDEX "Comment_story_id_idx" ON "public"."Comment"("story_id");

-- CreateIndex
CREATE INDEX "Comment_parent_id_idx" ON "public"."Comment"("parent_id");

-- CreateIndex
CREATE INDEX "Follow_follower_id_idx" ON "public"."Follow"("follower_id");

-- CreateIndex
CREATE INDEX "Follow_following_id_idx" ON "public"."Follow"("following_id");

-- CreateIndex
CREATE UNIQUE INDEX "Follow_follower_id_following_id_key" ON "public"."Follow"("follower_id", "following_id");

-- CreateIndex
CREATE INDEX "AI_Generation_user_id_idx" ON "public"."AI_Generation"("user_id");

-- CreateIndex
CREATE INDEX "AI_Generation_type_idx" ON "public"."AI_Generation"("type");

-- CreateIndex
CREATE INDEX "AI_Generation_status_idx" ON "public"."AI_Generation"("status");

-- CreateIndex
CREATE INDEX "AI_Generation_created_at_idx" ON "public"."AI_Generation"("created_at");

-- AddForeignKey
ALTER TABLE "public"."Subscription" ADD CONSTRAINT "Subscription_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Story" ADD CONSTRAINT "Story_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Chapter" ADD CONSTRAINT "Chapter_story_id_fkey" FOREIGN KEY ("story_id") REFERENCES "public"."Story"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Illustration" ADD CONSTRAINT "Illustration_story_id_fkey" FOREIGN KEY ("story_id") REFERENCES "public"."Story"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Illustration" ADD CONSTRAINT "Illustration_chapter_id_fkey" FOREIGN KEY ("chapter_id") REFERENCES "public"."Chapter"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Like" ADD CONSTRAINT "Like_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Like" ADD CONSTRAINT "Like_story_id_fkey" FOREIGN KEY ("story_id") REFERENCES "public"."Story"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Comment" ADD CONSTRAINT "Comment_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Comment" ADD CONSTRAINT "Comment_story_id_fkey" FOREIGN KEY ("story_id") REFERENCES "public"."Story"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Comment" ADD CONSTRAINT "Comment_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."Comment"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Follow" ADD CONSTRAINT "Follow_follower_id_fkey" FOREIGN KEY ("follower_id") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."Follow" ADD CONSTRAINT "Follow_following_id_fkey" FOREIGN KEY ("following_id") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."AI_Generation" ADD CONSTRAINT "AI_Generation_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
