// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            BigInt         @id @default(autoincrement())
  uuid          String         @unique @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email         String         @unique
  username      String         @unique
  password_hash String
  avatar_url    String?
  bio           String?
  role          Role           @default(user)
  created_at    DateTime       @default(now())
  updated_at    DateTime       @updatedAt
  last_login    DateTime?
  is_active     Boolean        @default(true)
  subscriptions Subscription[]
  stories       Story[]
  likes         Like[]
  comments      Comment[]
  follower      Follow[]       @relation("follower")
  following     Follow[]       @relation("following")
  ai_generations AI_Generation[]
}

model Subscription {
  id         BigInt    @id @default(autoincrement())
  user_id    BigInt
  plan_type  PlanType
  price      Decimal?  @db.Decimal(10, 2)
  start_date DateTime
  end_date   DateTime?
  is_active  Boolean   @default(true)
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model Story {
  id            BigInt         @id @default(autoincrement())
  user_id       BigInt
  title         String
  description   String?
  cover_image   String?
  story_data    Json?
  status        StoryStatus    @default(draft)
  visibility    Visibility     @default(public)
  view_count    Int            @default(0)
  like_count    Int            @default(0)
  comment_count Int            @default(0)
  created_at    DateTime       @default(now())
  updated_at    DateTime       @updatedAt
  published_at  DateTime?
  user          User           @relation(fields: [user_id], references: [id], onDelete: Cascade)
  chapters      Chapter[]
  illustrations Illustration[]
  likes         Like[]
  comments      Comment[]

  @@index([user_id])
  @@index([status])
  @@index([visibility])
  @@index([created_at])
}

model Chapter {
  id             BigInt         @id @default(autoincrement())
  story_id       BigInt
  title          String
  content        String?
  chapter_number Int
  image_url      String?
  chapter_data   Json?
  created_at     DateTime       @default(now())
  updated_at     DateTime       @updatedAt
  story          Story          @relation(fields: [story_id], references: [id], onDelete: Cascade)
  illustrations  Illustration[]

  @@index([story_id])
  @@index([story_id, chapter_number])
}

model Illustration {
  id         BigInt        @id @default(autoincrement())
  story_id   BigInt
  chapter_id BigInt?
  image_url  String
  prompt     String?
  style      String?
  image_data Json?
  status     IllustrationStatus @default(pending)
  created_at DateTime      @default(now())
  updated_at DateTime      @updatedAt
  story      Story         @relation(fields: [story_id], references: [id], onDelete: Cascade)
  chapter    Chapter?      @relation(fields: [chapter_id], references: [id], onDelete: SetNull)

  @@index([story_id])
  @@index([chapter_id])
  @@index([status])
}

model Like {
  id         BigInt   @id @default(autoincrement())
  user_id    BigInt
  story_id   BigInt
  created_at DateTime @default(now())
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  story      Story    @relation(fields: [story_id], references: [id], onDelete: Cascade)

  @@unique([user_id, story_id])
  @@index([user_id])
  @@index([story_id])
}

model Comment {
  id         BigInt    @id @default(autoincrement())
  user_id    BigInt
  story_id   BigInt
  content    String
  parent_id  BigInt?
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  user       User      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  story      Story     @relation(fields: [story_id], references: [id], onDelete: Cascade)
  parent     Comment?  @relation("CommentReplies", fields: [parent_id], references: [id], onDelete: Cascade)
  replies    Comment[] @relation("CommentReplies")

  @@index([user_id])
  @@index([story_id])
  @@index([parent_id])
}

model Follow {
  id           BigInt   @id @default(autoincrement())
  follower_id  BigInt
  following_id BigInt
  created_at   DateTime @default(now())
  follower     User     @relation("follower", fields: [follower_id], references: [id], onDelete: Cascade)
  following    User     @relation("following", fields: [following_id], references: [id], onDelete: Cascade)

  @@unique([follower_id, following_id])
  @@index([follower_id])
  @@index([following_id])
}

model AI_Generation {
  id            BigInt              @id @default(autoincrement())
  user_id       BigInt
  type          AIGenerationType
  prompt        Json
  response      Json?
  status        AIGenerationStatus  @default(pending)
  error_message String?
  created_at    DateTime            @default(now())
  completed_at  DateTime?
  user          User                @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
  @@index([type])
  @@index([status])
  @@index([created_at])
}

enum Role {
  user
  admin
  moderator
}

enum PlanType {
  free
  pro
  premium
}

enum StoryStatus {
  draft
  published
  archived
}

enum Visibility {
  public
  private
  unlisted
}

enum IllustrationStatus {
  pending
  processing
  completed
  failed
}

enum AIGenerationType {
  story
  chapter
  illustration
  character
}

enum AIGenerationStatus {
  pending
  processing
  completed
  failed
}