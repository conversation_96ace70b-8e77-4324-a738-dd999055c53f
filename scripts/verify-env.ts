import 'dotenv/config'
import { z } from 'zod'

const envSchema = z.object({
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url(),
  JWT_SECRET: z.string().min(1),
  OPENAI_API_KEY: z.string().min(1),
  NODE_ENV: z.enum(['development', 'production', 'test']),
})

try {
  envSchema.parse(process.env)
  console.log('✅ Environment variables are valid.')
} catch (error) {
  console.error('❌ Invalid environment variables:', error)
  process.exit(1)
}