{"name": "aid<PERSON>m", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@anthropic-ai/sdk": "^0.60.0", "@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.14.0", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@types/bcryptjs": "^3.0.0", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/pg": "^8.15.5", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "dotenv-cli": "^10.0.0", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.540.0", "multer": "^2.0.2", "next": "15.4.6", "next-auth": "^4.24.11", "openai": "^5.12.2", "pg": "^8.16.3", "prisma": "^6.14.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "redis": "^5.8.1", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "tsx": "^4.20.4", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^5.0.1", "@vitest/ui": "^3.2.4", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "15.4.6", "jsdom": "^26.1.0", "supertest": "^7.1.4", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5", "vitest": "^3.2.4"}}