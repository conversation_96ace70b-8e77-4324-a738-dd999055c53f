# AI童话故事书平台 - 系统架构设计文档

## 1. 项目概述

### 1.1 项目背景
基于PRD文档，AI童话故事书平台是一个集AI故事创作、UGC社区生态和商业化功能于一体的综合性平台。平台主要面向3-10岁儿童的父母，提供个性化、教育性的童话故事创作服务。

### 1.2 核心功能模块
- **AI故事创作引擎**：灵感注入、AI协作生成、预览与发布
- **UGC社区生态**：个人书架、发现广场、互动系统、用户主页
- **商业模式**：Freemium增值服务模式

## 2. 整体架构设计

### 2.1 架构选择：单体应用 + 微服务混合架构

考虑到项目初期规模和开发效率，采用**单体应用 + 微服务混合架构**：

#### 2.1.1 核心业务层 - 单体应用
使用Next.js 15作为主框架，构建前后端一体的单体应用：
- **前端**：Next.js 15 + TypeScript + Tailwind CSS + page-flip
- **后端**：Next.js API Routes
- **优势**：开发效率高、部署简单、类型安全、热重载

#### 2.1.2 AI服务层 - 微服务架构
将AI相关功能拆分为独立的微服务：
- **故事生成服务**：LLM API集成
- **图像生成服务**：Text-to-Image API集成
- **角色一致性服务**：角色锁定和风格统一
- **内容审核服务**：AI内容安全检测

#### 2.1.3 基础设施层
- **API网关**：统一入口、负载均衡、认证授权
- **服务注册与发现**：服务间通信
- **配置中心**：统一配置管理

### 2.2 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Web客户端] --> B[Next.js 15]
        C[移动端] --> B
        D[管理后台] --> B
    end
    
    subgraph "API网关层"
        E[API Gateway]
        E --> F[认证服务]
        E --> G[路由服务]
        E --> H[限流服务]
    end
    
    subgraph "核心业务层 - 单体应用"
        I[Next.js API Routes]
        I --> J[用户管理]
        I --> K[故事管理]
        I --> L[社区功能]
        I --> M[订阅管理]
        I --> N[文件上传]
    end
    
    subgraph "AI服务层 - 微服务"
        O[故事生成服务]
        P[图像生成服务]
        Q[角色一致性服务]
        R[内容审核服务]
    end
    
    subgraph "数据层"
        S[PostgreSQL]
        T[Redis]
        U[文件存储]
        V[搜索引擎]
    end
    
    subgraph "外部服务"
        W[OpenAI API]
        X[DALL-E API]
        Y[Midjourney API]
        Z[第三方支付]
    end
    
    B --> E
    E --> I
    I --> O
    I --> P
    I --> Q
    I --> R
    O --> W
    P --> X
    P --> Y
    I --> S
    I --> T
    I --> U
    I --> V
    I --> Z
    
    style A fill:#e1f5fe
    style C fill:#e1f5fe
    style D fill:#e1f5fe
    style E fill:#fff3e0
    style I fill:#f3e5f5
    style O fill:#e8f5e8
    style P fill:#e8f5e8
    style Q fill:#e8f5e8
    style R fill:#e8f5e8
    style S fill:#fce4ec
    style T fill:#fce4ec
    style U fill:#fce4ec
    style V fill:#fce4ec
    style W fill:#e3f2fd
    style X fill:#e3f2fd
    style Y fill:#e3f2fd
    style Z fill:#e3f2fd
```

### 2.3 数据流设计

```mermaid
graph LR
    subgraph "用户操作流"
        A[用户注册登录] --> B[故事创作]
        B --> C[AI生成内容]
        C --> D[编辑发布]
        D --> E[社区互动]
    end
    
    subgraph "AI处理流"
        F[用户输入] --> G[故事生成服务]
        G --> H[图像生成服务]
        H --> I[角色一致性检查]
        I --> J[内容审核]
        J --> K[返回结果]
    end
    
    subgraph "数据持久化流"
        L[用户数据] --> M[PostgreSQL]
        N[缓存数据] --> O[Redis]
        P[文件数据] --> Q[对象存储]
        R[搜索数据] --> S[Elasticsearch]
    end
```

## 3. 技术栈选型

### 3.1 前端技术栈
| 技术组件 | 版本 | 说明 |
|---------|------|------|
| Next.js | 15.4.6 | React全栈框架 |
| TypeScript | ^5 | 类型安全 |
| Tailwind CSS | ^4 | 样式框架 |
| React | 19.1.0 | UI框架 |
| React Query | ^5 | 数据获取和缓存 |
| React Hook Form | ^7 | 表单处理 |
| Zustand | ^5 | 状态管理 |

### 3.2 后端技术栈
| 技术组件 | 版本 | 说明 |
|---------|------|------|
| Node.js | ^20 | 运行时环境 |
| Next.js API Routes | 15.4.6 | API服务 |
| Prisma | ^5 | ORM框架 |
| PostgreSQL | ^15 | 主数据库 |
| Redis | ^7 | 缓存和会话存储 |
| JWT | ^9 | 认证机制 |
| Bull | ^4 | 任务队列 |
| Socket.io | ^4 | 实时通信 |

### 3.3 AI服务技术栈
| 技术组件 | 版本 | 说明 |
|---------|------|------|
| OpenAI API | ^4 | 故事生成 |
| DALL-E API | ^2 | 图像生成 |
| Midjourney API | ^1 | 插图生成 |
| Character Consistency | - | 角色锁定技术 |
| Content Moderation | - | 内容审核 |

### 3.4 DevOps技术栈
| 技术组件 | 版本 | 说明 |
|---------|------|------|
| Docker | ^24 | 容器化 |
| Docker Compose | ^2.24 | 容器编排 |
| GitHub Actions | - | CI/CD |
| Vercel | - | 部署平台 |
| AWS S3 | - | 对象存储 |
| AWS CloudFront | - | CDN加速 |

## 4. 数据库设计

### 4.1 数据库ER图

```mermaid
erDiagram
    USERS {
        bigint id PK
        uuid uuid
        string email UK
        string username
        string password_hash
        string avatar_url
        text bio
        enum role
        timestamp created_at
        timestamp updated_at
        timestamp last_login
        boolean is_active
    }
    
    SUBSCRIPTIONS {
        bigint id PK
        bigint user_id FK
        enum plan_type
        decimal price
        timestamp start_date
        timestamp end_date
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    STORIES {
        bigint id PK
        bigint user_id FK
        string title
        text description
        string cover_image
        json story_data
        enum status
        enum visibility
        integer view_count
        integer like_count
        integer comment_count
        timestamp created_at
        timestamp updated_at
        timestamp published_at
    }
    
    CHAPTERS {
        bigint id PK
        bigint story_id FK
        string title
        text content
        integer chapter_number
        string image_url
        json chapter_data
        timestamp created_at
        timestamp updated_at
    }
    
    ILLUSTRATIONS {
        bigint id PK
        bigint story_id FK
        bigint chapter_id FK
        string image_url
        string prompt
        string style
        json image_data
        enum status
        timestamp created_at
        timestamp updated_at
    }
    
    LIKES {
        bigint id PK
        bigint user_id FK
        bigint story_id FK
        timestamp created_at
    }
    
    COMMENTS {
        bigint id PK
        bigint user_id FK
        bigint story_id FK
        text content
        bigint parent_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    FOLLOWS {
        bigint id PK
        bigint follower_id FK
        bigint following_id FK
        timestamp created_at
    }
    
    AI_GENERATIONS {
        bigint id PK
        bigint user_id FK
        string type
        json prompt
        json response
        string status
        string error_message
        timestamp created_at
        timestamp completed_at
    }
    
    USERS ||--o{ SUBSCRIPTIONS : has
    USERS ||--o{ STORIES : creates
    USERS ||--o{ LIKES : gives
    USERS ||--o{ COMMENTS : writes
    USERS ||--o{ FOLLOWS : follows
    USERS ||--o{ AI_GENERATIONS : generates
    
    STORIES ||--o{ CHAPTERS : contains
    STORIES ||--o{ ILLUSTRATIONS : has
    STORIES ||--o{ LIKES : receives
    STORIES ||--o{ COMMENTS : receives
    
    CHAPTERS ||--o{ ILLUSTRATIONS : contains
    
    LIKES }|--|| USERS : from
    LIKES }|--|| STORIES : on
    COMMENTS }|--|| USERS : from
    COMMENTS }|--|| STORIES : on
    FOLLOWS }|--|| USERS : follower
    FOLLOWS }|--|| USERS : following
```

### 4.2 数据库表结构设计

#### 4.2.1 用户表 (users)
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    uuid UUID UNIQUE DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    bio TEXT,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);
```

#### 4.2.2 订阅表 (subscriptions)
```sql
CREATE TABLE subscriptions (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    plan_type VARCHAR(20) NOT NULL CHECK (plan_type IN ('free', 'pro', 'premium')),
    price DECIMAL(10,2),
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4.2.3 故事表 (stories)
```sql
CREATE TABLE stories (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    cover_image VARCHAR(500),
    story_data JSONB,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    visibility VARCHAR(20) DEFAULT 'public' CHECK (visibility IN ('public', 'private', 'unlisted')),
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP,
    INDEX idx_stories_user_id (user_id),
    INDEX idx_stories_status (status),
    INDEX idx_stories_visibility (visibility),
    INDEX idx_stories_created_at (created_at)
);
```

#### 4.2.4 章节表 (chapters)
```sql
CREATE TABLE chapters (
    id BIGSERIAL PRIMARY KEY,
    story_id BIGINT REFERENCES stories(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    chapter_number INTEGER NOT NULL,
    image_url VARCHAR(500),
    chapter_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_chapters_story_id (story_id),
    INDEX idx_chapters_chapter_number (story_id, chapter_number)
);
```

#### 4.2.5 插画表 (illustrations)
```sql
CREATE TABLE illustrations (
    id BIGSERIAL PRIMARY KEY,
    story_id BIGINT REFERENCES stories(id) ON DELETE CASCADE,
    chapter_id BIGINT REFERENCES chapters(id) ON DELETE SET NULL,
    image_url VARCHAR(500) NOT NULL,
    prompt TEXT,
    style VARCHAR(100),
    image_data JSONB,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_illustrations_story_id (story_id),
    INDEX idx_illustrations_chapter_id (chapter_id),
    INDEX idx_illustrations_status (status)
);
```

#### 4.2.6 点赞表 (likes)
```sql
CREATE TABLE likes (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    story_id BIGINT REFERENCES stories(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id, story_id),
    INDEX idx_likes_user_id (user_id),
    INDEX idx_likes_story_id (story_id)
);
```

#### 4.2.7 评论表 (comments)
```sql
CREATE TABLE comments (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    story_id BIGINT REFERENCES stories(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    parent_id BIGINT REFERENCES comments(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_comments_user_id (user_id),
    INDEX idx_comments_story_id (story_id),
    INDEX idx_comments_parent_id (parent_id)
);
```

#### 4.2.8 关注表 (follows)
```sql
CREATE TABLE follows (
    id BIGSERIAL PRIMARY KEY,
    follower_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    following_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (follower_id, following_id),
    INDEX idx_follows_follower_id (follower_id),
    INDEX idx_follows_following_id (following_id)
);
```

#### 4.2.9 AI生成记录表 (ai_generations)
```sql
CREATE TABLE ai_generations (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('story', 'chapter', 'illustration', 'character')),
    prompt JSONB NOT NULL,
    response JSONB,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    INDEX idx_ai_generations_user_id (user_id),
    INDEX idx_ai_generations_type (type),
    INDEX idx_ai_generations_status (status),
    INDEX idx_ai_generations_created_at (created_at)
);
```

## 5. API接口设计

### 5.1 API设计原则
- RESTful API设计
- 统一的响应格式
- JWT认证机制
- 权限控制
- 请求限流
- 输入验证

### 5.2 API接口清单

#### 5.2.1 用户认证相关API

```typescript
// 用户注册
POST /api/auth/register
{
  "email": string,
  "username": string,
  "password": string,
  "captcha": string
}

// 用户登录
POST /api/auth/login
{
  "email": string,
  "password": string
}

// 刷新Token
POST /api/auth/refresh
{
  "refreshToken": string
}

// 用户登出
POST /api/auth/logout
{}

// 获取当前用户信息
GET /api/auth/me
{}

// 修改密码
PUT /api/auth/password
{
  "oldPassword": string,
  "newPassword": string
}
```

#### 5.2.2 用户管理相关API

```typescript
// 获取用户信息
GET /api/users/:id
{}

// 更新用户信息
PUT /api/users/:id
{
  "username": string,
  "bio": string,
  "avatar": File
}

// 获取用户创建的故事
GET /api/users/:id/stories
{
  "page": number,
  "limit": number,
  "status": string
}

// 获取用户收藏的故事
GET /api/users/:id/favorites
{
  "page": number,
  "limit": number
}

// 获取用户关注列表
GET /api/users/:id/following
{
  "page": number,
  "limit": number
}

// 获取用户粉丝列表
GET /api/users/:id/followers
{
  "page": number,
  "limit": number
}

// 关注用户
POST /api/users/:id/follow
{}

// 取消关注用户
DELETE /api/users/:id/follow
{}
```

#### 5.2.3 故事创作相关API

```typescript
// 创建故事
POST /api/stories
{
  "title": string,
  "description": string,
  "theme": string,
  "elements": string[],
  "age_group": string,
  "style": string
}

// 获取故事列表
GET /api/stories
{
  "page": number,
  "limit": number,
  "category": string,
  "sort": string
}

// 获取故事详情
GET /api/stories/:id
{}

// 更新故事
PUT /api/stories/:id
{
  "title": string,
  "description": string,
  "status": string
}

// 删除故事
DELETE /api/stories/:id
{}

// 生成故事大纲
POST /api/stories/:id/outline
{
  "prompt": object
}

// 生成章节
POST /api/stories/:id/chapters
{
  "chapter_number": number,
  "prompt": object
}

// 生成插图
POST /api/stories/:id/illustrations
{
  "chapter_id": number,
  "prompt": object,
  "style": string
}

// 重新生成插图
POST /api/stories/:id/illustrations/:illustration_id/regenerate
{
  "prompt": string
}

// 发布故事
POST /api/stories/:id/publish
{}

// 导出故事
POST /api/stories/:id/export
{
  "format": string
}
```

#### 5.2.4 社区功能相关API

```typescript
// 点赞故事
POST /api/stories/:id/like
{}

// 取消点赞
DELETE /api/stories/:id/like
{}

// 获取故事点赞列表
GET /api/stories/:id/likes
{
  "page": number,
  "limit": number
}

// 评论故事
POST /api/stories/:id/comments
{
  "content": string,
  "parent_id": number
}

// 获取故事评论列表
GET /api/stories/:id/comments
{
  "page": number,
  "limit": number
}

// 更新评论
PUT /api/comments/:id
{
  "content": string
}

// 删除评论
DELETE /api/comments/:id
{}

// 收藏故事
POST /api/stories/:id/favorite
{}

// 取消收藏
DELETE /api/stories/:id/favorite
{}

// 获取收藏列表
GET /api/favorites
{
  "page": number,
  "limit": number
}
```

#### 5.2.5 发现广场相关API

```typescript
// 获取推荐故事
GET /api/discover/recommended
{
  "page": number,
  "limit": number
}

// 获取热门故事
GET /api/discover/trending
{
  "page": number,
  "limit": number
}

// 获取最新故事
GET /api/discover/latest
{
  "page": number,
  "limit": number
}

// 搜索故事
GET /api/discover/search
{
  "query": string,
  "category": string,
  "page": number,
  "limit": number
}

// 获取排行榜
GET /api/discover/rankings
{
  "type": string,
  "period": string
}

// 获取专题活动
GET /api/discover/activities
{
  "page": number,
  "limit": number
}
```

#### 5.2.6 订阅管理相关API

```typescript
// 获取订阅信息
GET /api/subscriptions
{}

// 创建订阅
POST /api/subscriptions
{
  "plan_type": string,
  "payment_method": string
}

// 取消订阅
DELETE /api/subscriptions
{}

// 获取订阅历史
GET /api/subscriptions/history
{
  "page": number,
  "limit": number
}

// 获取使用统计
GET /api/subscriptions/usage
{}
```

#### 5.2.7 文件上传相关API

```typescript
// 上传头像
POST /api/upload/avatar
{
  "file": File
}

// 上传故事封面
POST /api/upload/cover
{
  "file": File
}

// 上传插图
POST /api/upload/illustration
{
  "file": File
}

// 获取上传URL
GET /api/upload/url
{
  "filename": string,
  "contentType": string
}
```

#### 5.2.8 AI服务相关API

```typescript
// 生成故事
POST /api/ai/generate/story
{
  "prompt": object,
  "options": object
}

// 生成章节
POST /api/ai/generate/chapter
{
  "story_id": number,
  "prompt": object,
  "options": object
}

// 生成插图
POST /api/ai/generate/illustration
{
  "prompt": object,
  "style": string,
  "character_reference": string
}

// 生成角色
POST /api/ai/generate/character
{
  "prompt": object,
  "style": string
}

// 获取生成状态
GET /api/ai/status/:id
{}

// 获取生成历史
GET /api/ai/history
{
  "page": number,
  "limit": number
}
```

### 5.3 响应格式规范

#### 5.3.1 统一响应格式

```typescript
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: ValidationError[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

#### 5.3.2 错误响应格式

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}
```

#### 5.3.3 分页响应格式

```typescript
interface PaginatedResponse<T> {
  success: true;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
```

## 6. 安全性设计

### 6.1 认证与授权

#### 6.1.1 JWT认证机制
```typescript
// JWT配置
const jwtConfig = {
  secret: process.env.JWT_SECRET,
  expiresIn: '7d',
  refreshTokenExpiresIn: '30d'
};

// Token结构
{
  "sub": "user_id",
  "username": "username",
  "role": "user",
  "iat": 1640995200,
  "exp": 1643673600
}
```

#### 6.1.2 权限控制矩阵

| 角色 | 创建故事 | 编辑故事 | 删除故事 | 管理用户 | 内容审核 |
|------|---------|---------|---------|---------|---------|
| 游客 | ❌ | ❌ | ❌ | ❌ | ❌ |
| 普通用户 | ✅ | ✅(自己的) | ✅(自己的) | ❌ | ❌ |
| 编辑 | ✅ | ✅ | ✅(自己的) | ❌ | ✅ |
| 管理员 | ✅ | ✅ | ✅ | ✅ | ✅ |

### 6.2 数据安全

#### 6.2.1 密码安全
- 使用bcrypt进行密码哈希
- 密码复杂度要求：至少8位，包含大小写字母、数字、特殊字符
- 定期更换密码策略

#### 6.2.2 数据加密
- 敏感数据使用AES-256加密
- 传输层使用HTTPS
- 数据库连接使用SSL

#### 6.2.3 输入验证
- 前端和后端双重验证
- 使用Joi进行数据验证
- SQL注入防护
- XSS攻击防护

### 6.3 内容安全

#### 6.3.1 内容审核机制
```typescript
// 内容审核流程
const contentModeration = {
  // 1. 关键词过滤
  keywordFilter: {
    enabled: true,
    keywords: ['暴力', '色情', '政治', '敏感词'],
    action: 'reject'
  },
  
  // 2. AI内容检测
  aiDetection: {
    enabled: true,
    provider: 'openai',
    model: 'content-moderation-latest',
    threshold: 0.8
  },
  
  // 3. 人工审核
  humanReview: {
    enabled: true,
    queue: 'content-review',
    escalation: true
  }
};
```

#### 6.3.2 图片内容审核
- 图片内容识别
- 恶意图片检测
- 版权内容识别

### 6.4 API安全

#### 6.4.1 请求限流
```typescript
// 限流配置
const rateLimit = {
  // 登录限流
  login: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 5, // 最多5次
    message: '登录尝试次数过多，请稍后再试'
  },
  
  // API调用限流
  api: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 最多100次
    message: 'API调用频率过高，请稍后再试'
  }
};
```

#### 6.4.2 CORS配置
```typescript
// CORS配置
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 86400 // 24小时
};
```

## 7. 性能优化策略

### 7.1 缓存策略

#### 7.1.1 Redis缓存配置
```typescript
// Redis缓存配置
const redisConfig = {
  // 用户会话缓存
  sessions: {
    ttl: 7 * 24 * 60 * 60, // 7天
    prefix: 'session:'
  },
  
  // API响应缓存
  api: {
    ttl: 60 * 60, // 1小时
    prefix: 'api:'
  },
  
  // 页面缓存
  pages: {
    ttl: 30 * 60, // 30分钟
    prefix: 'page:'
  },
  
  // 热点数据缓存
  hotData: {
    ttl: 24 * 60 * 60, // 24小时
    prefix: 'hot:'
  }
};
```

#### 7.1.2 缓存策略
- **多级缓存**：本地缓存 + Redis缓存
- **缓存预热**：系统启动时加载热点数据
- **缓存更新**：主动更新 + 被动更新
- **缓存穿透防护**：布隆过滤器 + 空值缓存

### 7.2 CDN方案

#### 7.2.1 CDN配置
```typescript
// CDN配置
const cdnConfig = {
  // 静态资源CDN
  static: {
    provider: 'cloudflare',
    domain: 'cdn.aidream.com',
    cacheControl: 'public, max-age=31536000, immutable'
  },
  
  // 用户上传内容CDN
  userContent: {
    provider: 'cloudflare',
    domain: 'content.aidream.com',
    cacheControl: 'public, max-age=86400'
  },
  
  // AI生成内容CDN
  aiContent: {
    provider: 'cloudflare',
    domain: 'ai.aidream.com',
    cacheControl: 'public, max-age=86400'
  }
};
```

#### 7.2.2 CDN优化策略
- 静态资源缓存
- 图片压缩和格式优化
- 智能缓存刷新
- 边缘计算加速

### 7.3 数据库优化

#### 7.3.1 索引优化
```sql
-- 复合索引优化
CREATE INDEX idx_stories_user_status_created ON stories(user_id, status, created_at DESC);
CREATE INDEX idx_stories_category_created ON stories(category, created_at DESC);
CREATE INDEX idx_stories_trending ON stories(like_count, view_count, created_at DESC);

-- 全文搜索索引
CREATE INDEX idx_stories_search ON stories USING gin(to_tsvector('english', title || ' ' || description));
```

#### 7.3.2 查询优化
- 分页查询优化
- 连接查询优化
- 子查询优化
- 慢查询监控和优化

#### 7.3.3 数据库分片策略
- 按用户ID分片
- 按时间分片
- 按功能分片

### 7.4 前端性能优化

#### 7.4.1 代码分割
```typescript
// 路由级代码分割
const StoryCreation = dynamic(() => import('../components/StoryCreation'), {
  loading: () => <div>加载中...</div>
});

// 组件级代码分割
const IllustrationEditor = dynamic(() => import('../components/IllustrationEditor'), {
  loading: () => <div>加载中...</div>
});
```

#### 7.4.2 图片优化
```typescript
// 图片配置
const imageConfig = {
  // WebP格式支持
  formats: ['webp', 'avif', 'jpeg'],
  
  // 响应式图片
  responsive: true,
  
  // 图片质量
  quality: 80,
  
  // 图片尺寸
  sizes: [640, 750, 828, 1080, 1200, 1920],
  
  // 懒加载
  lazyLoad: true
};
```

#### 7.4.3 预加载策略
- 关键CSS预加载
- 字体预加载
- 关键JS预加载
- 资源预连接

### 7.5 AI服务优化

#### 7.5.1 请求优化
```typescript
// AI服务配置
const aiServiceConfig = {
  // 请求池
  pool: {
    min: 5,
    max: 20,
    idleTimeoutMillis: 30000
  },
  
  // 重试策略
  retry: {
    retries: 3,
    factor: 2,
    maxTimeout: 30000
  },
  
  // 缓存策略
  cache: {
    ttl: 3600, // 1小时
    maxSize: 1000
  }
};
```

#### 7.5.2 批量处理
- 批量生成章节
- 批量生成插图
- 异步任务处理

## 8. 部署架构设计

### 8.1 部署架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        A[Cloudflare CDN]
        B[Nginx负载均衡]
    end
    
    subgraph "应用层"
        C[Next.js应用服务器1]
        D[Next.js应用服务器2]
        E[Next.js应用服务器3]
    end
    
    subgraph "AI服务层"
        F[故事生成服务]
        G[图像生成服务]
        H[角色一致性服务]
        I[内容审核服务]
    end
    
    subgraph "数据层"
        J[PostgreSQL主库]
        K[PostgreSQL从库]
        L[Redis集群]
        M[对象存储]
        N[Elasticsearch集群]
    end
    
    subgraph "监控层"
        O[Prometheus]
        P[Grafana]
        Q[日志收集]
        R[告警系统]
    end
    
    A --> B
    B --> C
    B --> D
    B --> E
    C --> F
    C --> G
    C --> H
    C --> I
    D --> F
    D --> G
    D --> H
    D --> I
    E --> F
    E --> G
    E --> H
    E --> I
    F --> J
    G --> K
    H --> L
    I --> M
    C --> J
    C --> K
    C --> L
    C --> M
    C --> N
    F --> O
    G --> O
    H --> O
    I --> O
    O --> P
    O --> Q
    O --> R
    
    style A fill:#e3f2fd
    style B fill:#e3f2fd
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#f3e5f5
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style H fill:#e8f5e8
    style I fill:#e8f5e8
    style J fill:#fce4ec
    style K fill:#fce4ec
    style L fill:#fce4ec
    style M fill:#fce4ec
    style N fill:#fce4ec
    style O fill:#fff3e0
    style P fill:#fff3e0
    style Q fill:#fff3e0
    style R fill:#fff3e0
```

### 8.2 容器化部署

#### 8.2.1 Docker配置
```dockerfile
# Dockerfile
FROM node:20-alpine AS base

# 安装依赖
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# 复制依赖文件
COPY package.json pnpm-lock.yaml ./
RUN corepack enable && pnpm install --frozen-lockfile

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# 构建Next.js应用
RUN corepack enable && pnpm build

# 生产镜像
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# 设置正确的权限
RUN mkdir .next
RUN chown nextjs:nodejs .next

# 复制构建产物
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
```

#### 8.2.2 Docker Compose配置
```yaml
version: '3.8'

services:
  # 应用服务
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************/aidream
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret
      - OPENAI_API_KEY=your-openai-api-key
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=aidream
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Elasticsearch搜索
  elasticsearch:
    image: elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
```

### 8.3 CI/CD流程

#### 8.3.1 GitHub Actions配置
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install
        
      - name: Run tests
        run: pnpm test
        
      - name: Run linting
        run: pnpm lint
        
      - name: Run build
        run: pnpm build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'pnpm'
          
      - name: Install dependencies
        run: pnpm install
        
      - name: Build Docker image
        run: docker build -t aidream:latest .
        
      - name: Deploy to production
        run: |
          # 部署到生产环境
          ssh -i ${{ secrets.SSH_KEY }} ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_IP }} "
            docker stop aidream || true
            docker rm aidream || true
            docker run -d --name aidream -p 3000:3000 \
              -e DATABASE_URL=${{ secrets.DATABASE_URL }} \
              -e REDIS_URL=${{ secrets.REDIS_URL }} \
              -e JWT_SECRET=${{ secrets.JWT_SECRET }} \
              -e OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }} \
              aidream:latest
          "
```

### 8.4 监控与日志

#### 8.4.1 监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'nextjs-app'
    static_configs:
      - targets: ['app:3000']
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
```

#### 8.4.2 日志配置
```javascript
// 日志配置
const logConfig = {
  // 日志级别
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  
  // 日志格式
  format: 'combined',
  
  // 输出文件
  filename: 'logs/app.log',
  
  // 日志轮转
  maxsize: 5242880, // 5MB
  maxFiles: 5,
  
  // 错误日志
  errorLog: 'logs/error.log',
  
  // 访问日志
  accessLog: 'logs/access.log',
  
  // 日志传输
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' })
  ]
};
```

## 9. 测试策略

### 9.1 测试金字塔

```mermaid
graph TD
    subgraph "单元测试"
        A[单元测试 70%]
    end
    
    subgraph "集成测试"
        B[集成测试 20%]
    end
    
    subgraph "端到端测试"
        C[E2E测试 10%]
    end
    
    A --> B
    B --> C
```

### 9.2 测试框架配置

#### 9.2.1 单元测试
```typescript
// Jest配置
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/**/*.spec.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html']
};
```

#### 9.2.2 集成测试
```typescript
// 集成测试配置
describe('API Integration Tests', () => {
  let app: Express;
  let server: any;
  
  beforeAll(async () => {
    // 设置测试数据库
    await setupTestDatabase();
    
    // 创建测试应用
    app = createTestApp();
    
    // 启动测试服务器
    server = app.listen(0);
  });
  
  afterAll(async () => {
    // 关闭测试服务器
    await server.close();
    await cleanupTestDatabase();
  });
  
  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        });
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.token).toBeDefined();
    });
  });
});
```

#### 9.2.3 E2E测试
```typescript
// Playwright配置
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    
    // 收集测试时的截图和视频
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    
    // 移动端测试
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
});
```

### 9.3 测试数据管理

#### 9.3.1 测试数据工厂
```typescript
// 测试数据工厂
import { Factory } from 'fishery';

// 用户工厂
export const userFactory = Factory.define<User>(({ params }) => ({
  id: params.id || 1,
  email: params.email || '<EMAIL>',
  username: params.username || 'testuser',
  password_hash: params.password_hash || bcrypt.hashSync('password123', 10),
  created_at: params.created_at || new Date(),
  updated_at: params.updated_at || new Date(),
}));

// 故事工厂
export const storyFactory = Factory.define<Story>(({ params }) => ({
  id: params.id || 1,
  user_id: params.user_id || 1,
  title: params.title || '测试故事',
  description: params.description || '这是一个测试故事',
  status: params.status || 'published',
  visibility: params.visibility || 'public',
  created_at: params.created_at || new Date(),
  updated_at: params.updated_at || new Date(),
}));

// 章节工厂
export const chapterFactory = Factory.define<Chapter>(({ params }) => ({
  id: params.id || 1,
  story_id: params.story_id || 1,
  title: params.title || '第一章',
  content: params.content || '这是一个测试章节',
  chapter_number: params.chapter_number || 1,
  created_at: params.created_at || new Date(),
  updated_at: params.updated_at || new Date(),
}));
```

#### 9.3.2 测试数据库清理
```typescript
// 测试数据库清理
export const cleanupDatabase = async () => {
  // 清理用户表
  await prisma.user.deleteMany();
  
  // 清理故事表
  await prisma.story.deleteMany();
  
  // 清理章节表
  await prisma.chapter.deleteMany();
  
  // 清理插画表
  await prisma.illustration.deleteMany();
  
  // 清理点赞表
  await prisma.like.deleteMany();
  
  // 清理评论表
  await prisma.comment.deleteMany();
  
  // 清理关注表
  await prisma.follow.deleteMany();
  
  // 清理AI生成记录表
  await prisma.aiGeneration.deleteMany();
  
  // 清理订阅表
  await prisma.subscription.deleteMany();
};
```

## 10. 项目实施计划

### 10.1 开发阶段划分

#### 10.1.1 第一阶段：核心功能开发（4-6周）
- 用户认证系统
- 故事创作基础功能
- AI服务集成
- 基础UI界面

#### 10.1.2 第二阶段：社区功能开发（3-4周）
- 用户个人主页
- 故事发布系统
- 点赞评论功能
- 发现广场

#### 10.1.3 第三阶段：高级功能开发（3-4周）
- 角色一致性技术
- 内容审核系统
- 订阅付费功能
- 导出分享功能

#### 10.1.4 第四阶段：性能优化与部署（2-3周）
- 性能优化
- 安全加固
- 部署上线
- 监控系统

### 10.2 团队配置建议

#### 10.2.1 技术团队配置
- **项目经理**：1人
- **前端开发**：2-3人
- **后端开发**：2-3人
- **UI/UX设计师**：1人
- **测试工程师**：1人
- **DevOps工程师**：1人

#### 10.2.2 角色职责
- **项目经理**：项目规划、进度管理、团队协调
- **前端开发**：UI实现、交互开发、性能优化
- **后端开发**：API开发、数据库设计、AI服务集成
- **UI/UX设计师**：界面设计、用户体验优化
- **测试工程师**：测试用例编写、质量保证
- **DevOps工程师**：部署运维、监控系统

### 10.3 风险评估与应对

#### 10.3.1 技术风险
- **AI服务稳定性**：多服务商备份、降级策略
- **性能瓶颈**：压力测试、性能监控
- **安全漏洞**：安全审计、漏洞扫描

#### 10.3.2 业务风险
- **用户增长**：用户反馈收集、功能迭代
- **市场竞争**：差异化功能、用户体验优化
- **法规合规**：内容审核、数据保护

## 11. 总结

本文档提供了AI童话故事书平台的完整系统架构设计方案，包括：

1. **整体架构设计**：采用单体应用 + 微服务混合架构
2. **技术栈选型**：Next.js 15 + TypeScript + Tailwind CSS + PostgreSQL + Redis
3. **数据库设计**：完整的ER图和表结构设计
4. **API接口设计**：RESTful API接口规范
5. **安全性设计**：JWT认证、权限控制、内容审核
6. **性能优化**：缓存策略、CDN、数据库优化
7. **部署架构**：容器化部署、CI/CD流程、监控系统
8. **测试策略**：单元测试、集成测试、E2E测试
9. **实施计划**：开发阶段划分、团队配置、风险评估

该架构设计充分考虑了项目的可扩展性、安全性、性能和可维护性，为项目的成功实施提供了坚实的技术基础。